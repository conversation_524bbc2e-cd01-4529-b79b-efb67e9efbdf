import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
    {
        path: '/',
        name: 'Overview',
        component: () => import('../views/Overview.vue')
    },
    {
        path: '/scene/new',
        name: 'NewScene',
        component: () => import('../views/scene/NewScene.vue')
    },
    {
        path: '/scene/edit/:id',
        name: 'EditScene',
        component: () => import('../views/scene/EditScene.vue')
    },
    {
        path: '/scene/manage',
        name: 'ManageScene',
        component: () => import('../views/scene/ManageScene.vue')
    },
    {
        path: '/scene/history/:id',
        name: 'SceneHistory',
        component: () => import('../views/scene/SceneHistory.vue')
    }
]

const router = new VueRouter({
    mode: 'history',
    base: process.env.BASE_URL,
    routes
})

export default router