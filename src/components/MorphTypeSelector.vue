<template>
  <!-- 多选模式：使用复选框组 -->
  <el-checkbox-group
    v-if="multiple"
    v-model="selectedValue"
    :disabled="disabled"
    @change="handleChange">
    <el-checkbox
      v-for="morphType in filteredMorphTypes"
      :key="morphType.ID"
      :label="morphType.ID">
      {{ morphType.morph_name }}
    </el-checkbox>
  </el-checkbox-group>

  <!-- 单选模式：使用下拉选择器 -->
  <el-select
    v-else
    v-model="selectedValue"
    placeholder="请选择型态类型"
    :disabled="disabled"
    clearable
    style="width: 100%"
    @change="handleChange"
  >
    <el-option
      v-for="morphType in filteredMorphTypes"
      :key="morphType.ID"
      :label="morphType.morph_name"
      :value="morphType.ID"
    >
    </el-option>
  </el-select>
</template>

<script>
import { getFilteredMorphTypes } from '@/utils/morphTypeUtils'

export default {
  name: 'MorphTypeSelector',
  props: {
    // 当前选中的值 - 支持单个值或数组
    value: {
      type: [String, Number, Array],
      default: null
    },
    // 所有型态选项列表
    morphTypes: {
      type: Array,
      default: () => []
    },
    // 平台信息（用于过滤）
    platform: {
      type: Object,
      default: null
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否多选模式
    multiple: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    selectedValue: {
      get() {
        //console.log('🔍 MorphTypeSelector selectedValue getter:')
        //console.log('  - 输入值 (this.value):', this.value, '类型:', typeof this.value)
        //console.log('  - 是否多选 (this.multiple):', this.multiple)
        //console.log('  - 平台信息:', this.platform && this.platform.platform_name, 'allow_multi_release_modes:', this.platform && this.platform.allow_multi_release_modes)

        if (this.multiple) {
          // 多选模式：确保返回数组格式
          if (Array.isArray(this.value)) {
            // 将数组中的值转换为数字类型
            const result = this.value.map(v => {
              const numValue = Number(v)
              return isNaN(numValue) ? v : numValue
            })
            //console.log('  - 多选模式 - 数组输入，转换结果:', result)
            return result
          } else if (typeof this.value === 'string' && this.value.startsWith('[') && this.value.endsWith(']')) {
            // 🔥 处理字符串格式的数组，如 "[1,2,3]"
            try {
              const parsed = JSON.parse(this.value)
              if (Array.isArray(parsed)) {
                const result = parsed.map(v => {
                  const numValue = Number(v)
                  return isNaN(numValue) ? v : numValue
                })
                //console.log('  - 多选模式 - 字符串数组输入，解析结果:', this.value, '→', parsed, '→', result)
                return result
              } else {
                //console.log('  - 多选模式 - 字符串数组解析后不是数组，返回空数组')
                return []
              }
            } catch (error) {
              //console.log('  - 多选模式 - 字符串数组解析失败，返回空数组:', error)
              return []
            }
          } else if (this.value !== null && this.value !== undefined && this.value !== '') {
            // 如果传入的是单个值，转换为数组
            const numValue = Number(this.value)
            const result = [isNaN(numValue) ? this.value : numValue]
            //console.log('  - 多选模式 - 单值输入，转换为数组:', result)
            return result
          } else {
            //console.log('  - 多选模式 - 空值输入，返回空数组: []')
            return []
          }
        } else {
          // 单选模式：确保返回单个值
          if (this.value === null || this.value === undefined || this.value === '') {
            //console.log('  - 单选模式 - 空值输入，返回 null')
            return null
          }

          // 如果传入的是数组，取第一个值
          if (Array.isArray(this.value)) {
            if (this.value.length > 0) {
              const numValue = Number(this.value[0])
              const result = isNaN(numValue) ? this.value[0] : numValue
              //console.log('  - 单选模式 - 数组输入，取第一个值:', this.value[0], '→', result)
              return result
            } else {
              //console.log('  - 单选模式 - 空数组输入，返回 null')
              return null
            }
          }

          // 🔥 处理字符串格式的数组，如 "[8]"
          if (typeof this.value === 'string' && this.value.startsWith('[') && this.value.endsWith(']')) {
            try {
              const parsed = JSON.parse(this.value)
              if (Array.isArray(parsed) && parsed.length > 0) {
                const numValue = Number(parsed[0])
                const result = isNaN(numValue) ? parsed[0] : numValue
                //console.log('  - 单选模式 - 字符串数组输入，解析并取第一个值:', this.value, '→', parsed, '→', result)
                return result
              } else {
                //console.log('  - 单选模式 - 字符串数组为空，返回 null')
                return null
              }
            } catch (error) {
              //console.log('  - 单选模式 - 字符串数组解析失败，按普通字符串处理:', error)
              // 解析失败，按普通字符串处理
            }
          }

          // 将值转换为数字类型，因为 morphType.ID 是数字
          const numValue = Number(this.value)
          const result = isNaN(numValue) ? this.value : numValue
          //console.log('  - 单选模式 - 单值输入:', this.value, '→', result)
          return result
        }
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    // 根据平台过滤后的型态选项
    filteredMorphTypes() {
      const result = getFilteredMorphTypes(this.morphTypes, this.platform)
      //console.log('🎯 MorphTypeSelector filteredMorphTypes:')
      //console.log('  - 平台:', this.platform && this.platform.platform_name)
      //console.log('  - 平台 published_morph_ids:', this.platform && this.platform.published_morph_ids)
      //console.log('  - 所有型态选项数量:', this.morphTypes && this.morphTypes.length || 0)
      //console.log('  - 过滤后选项数量:', result && result.length || 0)
      //console.log('  - 过滤后选项:', result && result.map(item => ({ ID: item.ID, name: item.morph_name || item.dict_name })))
      return result
    }
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    }
  }
}
</script>
