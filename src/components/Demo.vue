<template>
  <div class="demo-container">
    <h2>多选标签组件演示</h2>
    
    <div class="demo-section">
      <h3>1. 基础多选标签组件</h3>
      <div class="demo-item">
        <label>选择颜色：</label>
        <multi-select-tags
          v-model="selectedColors"
          :options="colorOptions"
          option-key="id"
          option-label="name"
          placeholder="请选择颜色"
        />
        <p>选中的值: {{ selectedColors }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h3>2. 模态选择器</h3>
      <div class="demo-item">
        <label>选择模态：</label>
        <modality-selector
          v-model="selectedModality"
          :modality-options="modalityOptions"
        />
        <p>选中的值: {{ selectedModality }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h3>3. 型态选择器</h3>
      <div class="demo-item">
        <label>选择型态：</label>
        <morph-type-selector
          v-model="selectedMorphType"
          :morph-types="morphTypes"
          :platform="platform"
        />
        <p>选中的值: {{ selectedMorphType }}</p>
        <p>平台限制的型态ID: {{ platform.published_morph_ids }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h3>4. 禁用状态</h3>
      <div class="demo-item">
        <label>禁用的选择器：</label>
        <multi-select-tags
          v-model="selectedColors"
          :options="colorOptions"
          option-key="id"
          option-label="name"
          placeholder="已禁用"
          :disabled="true"
        />
      </div>
    </div>
  </div>
</template>

<script>
import MultiSelectTags from './MultiSelectTags.vue'
import ModalitySelector from './ModalitySelector.vue'
import MorphTypeSelector from './MorphTypeSelector.vue'

export default {
  name: 'Demo',
  components: {
    MultiSelectTags,
    ModalitySelector,
    MorphTypeSelector
  },
  data() {
    return {
      // 基础演示数据
      selectedColors: [1, 3],
      colorOptions: [
        { id: 1, name: '红色' },
        { id: 2, name: '绿色' },
        { id: 3, name: '蓝色' },
        { id: 4, name: '黄色' },
        { id: 5, name: '紫色' }
      ],
      
      // 模态演示数据
      selectedModality: [1, 2],
      modalityOptions: [
        { id: 1, dict_name: '视频' },
        { id: 2, dict_name: '音频' },
        { id: 3, dict_name: '卡通视频' },
        { id: 4, dict_name: '语音（播客音频、MP3）' },
        { id: 5, dict_name: '游戏' }
      ],
      
      // 型态演示数据
      selectedMorphType: [5],
      morphTypes: [
        { ID: 1, morph_name: '型态1' },
        { ID: 2, morph_name: '型态2' },
        { ID: 3, morph_name: '型态3' },
        { ID: 4, morph_name: '型态4' },
        { ID: 5, morph_name: '型态5' },
        { ID: 6, morph_name: '型态6' },
        { ID: 7, morph_name: '型态7' },
        { ID: 8, morph_name: '型态8' }
      ],
      platform: {
        platform_id: 1,
        platform_name: '测试平台',
        published_morph_ids: ['5', '8'] // 只允许选择型态5和型态8
      }
    }
  }
}
</script>

<style scoped>
.demo-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.demo-item {
  margin-bottom: 15px;
}

.demo-item label {
  display: inline-block;
  width: 120px;
  font-weight: bold;
  margin-bottom: 8px;
}

.demo-item p {
  margin-top: 8px;
  color: #666;
  font-size: 14px;
}

h2 {
  color: #303133;
  margin-bottom: 20px;
}

h3 {
  color: #606266;
  margin-bottom: 15px;
}
</style>
