<template>
  <multi-select-tags
    v-model="selectedValues"
    :options="modalityOptions"
    option-key="id"
    option-label="dict_name"
    placeholder="请选择模态类型"
    :disabled="disabled"
    @change="handleChange"
  />
</template>

<script>
import MultiSelectTags from './MultiSelectTags.vue'

export default {
  name: 'ModalitySelector',
  components: {
    MultiSelectTags
  },
  props: {
    // 当前选中的值
    value: {
      type: Array,
      default: () => []
    },
    // 模态选项列表
    modalityOptions: {
      type: Array,
      default: () => []
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    selectedValues: {
      get() {
        return this.value || []
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    handleChange(values) {
      this.$emit('change', values)
    }
  }
}
</script>
