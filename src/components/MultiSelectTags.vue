<template>
  <div class="multi-select-tags">
    <div class="tags-container" @click="toggleDropdown">
      <!-- 已选择的标签 -->
      <div class="selected-tags">
        <el-tag
          v-for="item in selectedItems"
          :key="getItemKey(item)"
          :closable="!disabled"
          @close="removeTag(item)"
          type="primary"
          size="small"
          class="selected-tag"
        >
          {{ getItemLabel(item) }}
        </el-tag>
        
        <!-- 占位符 -->
        <span v-if="selectedItems.length === 0" class="placeholder">
          {{ placeholder }}
        </span>
      </div>
      
      <!-- 下拉箭头 -->
      <i 
        :class="['el-icon-arrow-down', 'dropdown-arrow', { 'is-reverse': dropdownVisible }]"
        @click.stop="toggleDropdown"
      ></i>
    </div>
    
    <!-- 下拉选项列表 -->
    <transition name="el-zoom-in-top">
      <div v-show="dropdownVisible" class="dropdown-menu">
        <div class="dropdown-content">
          <div
            v-for="option in options"
            :key="getOptionKey(option)"
            :class="['dropdown-item', { 'is-selected': isSelected(option) }]"
            @click="toggleOption(option)"
          >
            <el-checkbox 
              :value="isSelected(option)"
              @change="toggleOption(option)"
              @click.stop
            >
              {{ getOptionLabel(option) }}
            </el-checkbox>
          </div>
          
          <!-- 无选项提示 -->
          <div v-if="options.length === 0" class="no-options">
            暂无选项
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'MultiSelectTags',
  props: {
    // 当前选中的值（数组）
    value: {
      type: Array,
      default: () => []
    },
    // 选项列表
    options: {
      type: Array,
      default: () => []
    },
    // 选项的键字段名
    optionKey: {
      type: String,
      default: 'id'
    },
    // 选项的标签字段名
    optionLabel: {
      type: String,
      default: 'name'
    },
    // 占位符
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dropdownVisible: false
    }
  },
  computed: {
    selectedItems() {
      return this.options.filter(option => 
        this.value.includes(this.getOptionKey(option))
      )
    }
  },
  mounted() {
    // 点击外部关闭下拉框
    document.addEventListener('click', this.handleClickOutside)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside)
  },
  methods: {
    // 获取选项的键值
    getOptionKey(option) {
      return option[this.optionKey]
    },
    // 获取选项的标签
    getOptionLabel(option) {
      return option[this.optionLabel]
    },
    // 获取已选项的键值
    getItemKey(item) {
      return this.getOptionKey(item)
    },
    // 获取已选项的标签
    getItemLabel(item) {
      return this.getOptionLabel(item)
    },
    // 判断选项是否被选中
    isSelected(option) {
      return this.value.includes(this.getOptionKey(option))
    },
    // 切换下拉框显示状态
    toggleDropdown() {
      if (!this.disabled) {
        this.dropdownVisible = !this.dropdownVisible
      }
    },
    // 切换选项选中状态
    toggleOption(option) {
      const key = this.getOptionKey(option)
      const newValue = [...this.value]
      const index = newValue.indexOf(key)
      
      if (index > -1) {
        newValue.splice(index, 1)
      } else {
        newValue.push(key)
      }
      
      this.$emit('input', newValue)
      this.$emit('change', newValue)
    },
    // 移除标签
    removeTag(item) {
      const key = this.getItemKey(item)
      const newValue = this.value.filter(val => val !== key)
      this.$emit('input', newValue)
      this.$emit('change', newValue)
    },
    // 点击外部关闭下拉框
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.dropdownVisible = false
      }
    }
  }
}
</script>

<style scoped>
.multi-select-tags {
  position: relative;
  width: 100%;
}

.tags-container {
  min-height: 32px;
  padding: 4px 30px 4px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  position: relative;
  display: flex;
  align-items: center;
}

.tags-container:hover {
  border-color: #c0c4cc;
}

.tags-container:focus-within {
  border-color: #409eff;
}

.selected-tags {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.selected-tag {
  margin: 0;
}

.placeholder {
  color: #c0c4cc;
  font-size: 14px;
}

.dropdown-arrow {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #c0c4cc;
  transition: transform 0.3s;
  cursor: pointer;
}

.dropdown-arrow.is-reverse {
  transform: translateY(-50%) rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-top: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-content {
  padding: 4px 0;
}

.dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: #f5f7fa;
}

.dropdown-item.is-selected {
  background-color: #ecf5ff;
}

.no-options {
  padding: 12px;
  text-align: center;
  color: #999;
  font-size: 14px;
}

/* 过渡动画 */
.el-zoom-in-top-enter-active,
.el-zoom-in-top-leave-active {
  opacity: 1;
  transform: scaleY(1);
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  transform-origin: center top;
}

.el-zoom-in-top-enter,
.el-zoom-in-top-leave-to {
  opacity: 0;
  transform: scaleY(0);
}
</style>
