# 多选标签组件使用说明

## 组件介绍

本目录包含了三个相关的组件：

1. **MultiSelectTags.vue** - 基础的多选标签组件
2. **ModalitySelector.vue** - 模态选择器组件
3. **MorphTypeSelector.vue** - 型态选择器组件

## 使用方法

### 1. 在 NewScene.vue 中使用

```vue
<template>
  <!-- 模态选择 -->
  <el-form-item label="模态" :prop="'output_platforms_data.' + platform.platform_id + '.multimodal_array'">
    <modality-selector
      v-model="form.output_platforms_data[platform.platform_id].multimodal_array"
      :modality-options="modalityOptions"
    />
  </el-form-item>

  <!-- 型态选择 -->
  <el-form-item label="型态" :prop="'output_platforms_data.' + platform.platform_id + '.morph_type'">
    <morph-type-selector
      v-model="form.output_platforms_data[platform.platform_id].morph_type"
      :morph-types="morphTypes"
      :platform="platform"
    />
  </el-form-item>
</template>

<script>
import ModalitySelector from '@/components/ModalitySelector.vue'
import MorphTypeSelector from '@/components/MorphTypeSelector.vue'

export default {
  components: {
    ModalitySelector,
    MorphTypeSelector
  },
  // ... 其他代码
}
</script>
```

### 2. 在 EditScene.vue 中使用

使用方法与 NewScene.vue 相同，只需要导入组件并在模板中使用即可。

## 组件特性

### MultiSelectTags 基础组件

**Props:**
- `value` (Array): 当前选中的值数组
- `options` (Array): 选项列表
- `optionKey` (String): 选项的键字段名，默认 'id'
- `optionLabel` (String): 选项的标签字段名，默认 'name'
- `placeholder` (String): 占位符文本
- `disabled` (Boolean): 是否禁用

**Events:**
- `input`: 值改变时触发
- `change`: 值改变时触发

### ModalitySelector 模态选择器

**Props:**
- `value` (Array): 当前选中的模态ID数组
- `modalityOptions` (Array): 模态选项列表
- `disabled` (Boolean): 是否禁用

### MorphTypeSelector 型态选择器

**Props:**
- `value` (String|Number): 当前选中的型态ID（单选）
- `morphTypes` (Array): 所有型态选项列表
- `platform` (Object): 平台信息（用于过滤可选型态）
- `disabled` (Boolean): 是否禁用

## 样式特点

1. **标签式显示**: 选中的选项以蓝色标签形式显示
2. **可删除**: 每个标签都有删除按钮
3. **下拉选择**: 点击可展开选项列表
4. **复选框**: 下拉列表中使用复选框显示选中状态
5. **响应式**: 支持鼠标悬停和焦点状态
6. **动画效果**: 下拉展开/收起有平滑动画

## 替换步骤

1. 在页面中导入新组件
2. 替换原有的 el-select 组件
3. 调整 v-model 绑定（确保绑定的是数组）
4. 测试功能是否正常

## 注意事项

1. 确保 `modalityOptions` 数据结构包含 `id` 和 `dict_name` 字段
2. 确保 `morphTypes` 数据结构包含 `ID` 和 `morph_name` 字段
3. 组件会自动处理数组格式的数据绑定
4. 型态选择器会根据平台的 `published_morph_ids` 自动过滤可选项
