/**
 * 全局组件注册
 */

import MultiSelectTags from './MultiSelectTags.vue'
import ModalitySelector from './ModalitySelector.vue'
import MorphTypeSelector from './MorphTypeSelector.vue'

// 组件列表
const components = [
  MultiSelectTags,
  ModalitySelector,
  MorphTypeSelector
]

// 定义 install 方法，接收 Vue 作为参数
const install = function (Vue) {
  // 遍历注册全局组件
  components.forEach(component => {
    Vue.component(component.name, component)
  })
}

// 判断是否是直接引入文件，如果是，就不用调用 Vue.use()
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue)
}

export default {
  install,
  MultiSelectTags,
  ModalitySelector,
  MorphTypeSelector
}

export {
  MultiSelectTags,
  ModalitySelector,
  MorphTypeSelector
}
