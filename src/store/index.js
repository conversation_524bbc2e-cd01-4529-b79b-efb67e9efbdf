import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
    state: {
        scenes: [],
        platforms: [],
        morphTypes: [],
        models: [],
        currentScene: null,
        loading: false,
    },
    mutations: {
        SET_SCENES(state, scenes) {
            state.scenes = scenes
        },
        SET_PLATFORMS(state, platforms) {
            state.platforms = platforms
        },
        SET_MORPH_TYPES(state, value) {
            state.morphTypes = value
        },
        SET_MODELS(state, value) {
            state.models = value
        },
        SET_CURRENT_SCENE(state, scene) {
            state.currentScene = scene
        },
        SET_LOADING(state, loading) {
            state.loading = loading
        }
    },
    actions: {
        async fetchScenes({ commit }, { page = 1, pageSize = 15 }) {
            commit('SET_LOADING', true)
            try {
                const response = await this._vm.$http.post('', {
                    api: '/api/scene/getList',
                    param: { page, pageSize }
                })
                console.log(response)
                commit('SET_SCENES', response.data.data)
            } catch (error) {
                console.error('Error fetching scenes:', error)
            } finally {
                commit('SET_LOADING', false)
            }
        },
        async fetchRecentScenes({ commit }) {
            commit('SET_LOADING', true)
            try {
                const response = await this._vm.$http.post('', {
                    api: '/api/scene/getRecent'
                })
                console.log(response)
                commit('SET_SCENES', response.data.data)
            } catch (error) {
                console.error('Error fetching recent scenes:', error)
            } finally {
                commit('SET_LOADING', false)
            }
        },
        async fetchPlatforms({ commit }, { page = 1, pageSize = 15 }) {
            try {
                const response = await this._vm.$http.post('', {
                    api: '/api/platform/getList',
                    param: { page, pageSize }
                })
                commit('SET_PLATFORMS', response.data.data)
            } catch (error) {
                console.error('Error fetching platforms:', error)
            }
        },
        async fetchMorphTypes({ commit }) {
            try {
                const response = await this._vm.$http.post('', {
                    api: '/api/dict/getList',
                    param: {
                        dict_type: 'publish'
                    }
                })
                commit('SET_MORPH_TYPES', response.data.data)
            } catch (error) {
                console.error('Error fetching platforms:', error)
            }
        },
        // 查询模型
        async fetchModels({ commit }) {
            try {
                const response = await this._vm.$http.post('', {
                    api: '/api/dict/getList',
                    param: {
                        dict_type: 'models'
                    }
                })

                // 按 model_recommend_ranking 升序排列
                let models = response.data.data || []
                models.sort((a, b) => {
                    const rankingA = a.model_recommend_ranking || 999999
                    const rankingB = b.model_recommend_ranking || 999999
                    return rankingA - rankingB
                })

                commit('SET_MODELS', models)

                // 返回默认模型的ID（is_default为1的模型）
                const defaultModel = models.find(model => model.is_default === 1)
                return defaultModel ? defaultModel.model_id : null
            } catch (error) {
                console.error('Error fetching models:', error)
                return null
            }
        }
    }
})
