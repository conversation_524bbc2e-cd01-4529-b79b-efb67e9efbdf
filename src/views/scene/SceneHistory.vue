<template>
    <div class="scene-history">
        <el-card class="history-card">
            <div slot="header" class="header-with-breadcrumb">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item :to="{ path: '/scene/manage' }">场景管理</el-breadcrumb-item>
                    <el-breadcrumb-item>{{ sceneName }}</el-breadcrumb-item>
                    <el-breadcrumb-item>运行记录</el-breadcrumb-item>
                </el-breadcrumb>
            </div>

            <div class="history-header">
                <div class="scene-info">
                    <h3>{{ sceneName }}</h3>
                </div>
                <!-- <div class="history-actions">
                    <el-button type="primary" size="small" @click="openAIOptimize">
                        AI优化提示词
                    </el-button>
                </div> -->
            </div>

            <el-table :data="historyList" v-loading="historyLoading">
                <el-table-column prop="note_title" label="标题" />
                <el-table-column prop="used_model_name" label="使用模型" />
                <el-table-column prop="create_time" label="创建时间" width="200"/>
                <el-table-column label="操作" width="160">
                    <template slot-scope="{ row }">
                        <el-button type="text" @click="handleShowDetail(row)">查看详情</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination">
                <el-pagination @size-change="handleHistorySizeChange" @current-change="handleHistoryCurrentChange"
                    :current-page="historyCurrentPage" :page-sizes="[10, 15, 20, 30]" :page-size="historyPageSize"
                    layout="total, sizes, prev, pager, next" :total="historyTotal" />
            </div>
        </el-card>

        <el-dialog title="生成内容" :visible.sync="detailDialogVisible" width="80%">
            <div style="padding: 20px;">
                <div class="content-section" v-if="detailData.note_title">
                    <h3 class="section-title">标题</h3>
                    <div class="section-content" v-html="renderMarkdown(detailData.note_title)"></div>
                </div>

                <div class="content-section" v-if="detailData.note_text">
                    <h3 class="section-title">内容</h3>
                    <div class="section-content" v-html="renderMarkdown(detailData.note_text)"></div>
                </div>

                <div class="content-section" v-if="detailData.key_words">
                    <h3 class="section-title">关键词</h3>
                    <div class="section-content">
                        <el-tag
                            v-for="(keyword, index) in parseKeywords(detailData.key_words)"
                            :key="index"
                            type="info"
                            size="small"
                            style="margin-right: 8px; margin-bottom: 8px;">
                            {{ keyword }}
                        </el-tag>
                    </div>
                </div>
                <div class="content-section" v-if="detailData.picture_url">
                    <h3 class="section-title">图片</h3>
                    <div class="section-content">
                        <div class="image-gallery">
                            <div
                                v-for="(imageUrl, index) in parsePictureUrls(detailData.picture_url)"
                                :key="index"
                                class="image-item">
                                <el-image
                                    :src="imageUrl"
                                    fit="cover"
                                    style="width: 200px; height: 150px; border-radius: 4px;"
                                    :preview-src-list="parsePictureUrls(detailData.picture_url)"
                                    :initial-index="index">
                                    <div slot="error" class="image-slot">
                                        <i class="el-icon-picture-outline"></i>
                                        <div>加载失败</div>
                                    </div>
                                </el-image>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import marked from 'marked'

export default {
    name: 'SceneHistory',
    data() {
        return {
            sceneId: null,
            sceneName: '',
            historyList: [],
            historyCurrentPage: 1,
            historyPageSize: 15,
            historyTotal: 0,
            historyLoading: false,
            detailDialogVisible: false,
            detailData: {
                ai: '',
                outline: '',
                prompt: '',
                content: ''
            }
        }
    },
    methods: {
        async fetchHistory() {
            this.historyLoading = true
            try {
                const response = await this.$http.post('', {
                    api: '/api/scene/getHistoryList',
                    param: {
                        scene_id: this.sceneId,
                        page: this.historyCurrentPage,
                        pageSize: this.historyPageSize
                    }
                })
                this.historyList = response.data.data
                this.historyTotal = response.data.total
            } catch (error) {
                this.$message.error('获取历史记录失败')
                console.error('Error fetching history:', error)
            } finally {
                this.historyLoading = false
            }
        },
        handleHistorySizeChange(val) {
            this.historyPageSize = val
            this.fetchHistory()
        },
        handleHistoryCurrentChange(val) {
            this.historyCurrentPage = val
            this.fetchHistory()
        },
        async openAIOptimize() {
            const url = `https://acpfbbeg.manus.space/?scene_id=${this.sceneId}`
            window.open(url, '_blank')
        },
        async handleShowDetail(row) {
            console.info(row)
            this.detailDialogVisible = true
            try {
                this.detailData = row
            } catch (e) {
                this.detailData = { ai: '获取失败', outline: '', prompt: '', content: '' }
            }
        },
        renderMarkdown(md) {
            if (!md) return ''
            return marked(md)
        },
        parseKeywords(keywordsData) {
            if (!keywordsData) return []

            try {
                keywordsData = keywordsData.replace(/'/g, '"');
                // 如果已经是数组，直接返回
                if (Array.isArray(keywordsData)) {
                    return keywordsData
                }

                // 如果是字符串，尝试解析JSON
                if (typeof keywordsData === 'string') {
                    const parsed = JSON.parse(keywordsData)
                    return Array.isArray(parsed) ? parsed : [keywordsData]
                }

                return [keywordsData]
            } catch (error) {
                console.error('解析关键词失败:', error)
                // 如果解析失败，尝试按逗号分割
                return typeof keywordsData === 'string' ? keywordsData.split(',').map(item => item.trim()) : []
            }
        },

        parsePictureUrls(pictureData) {
            if (!pictureData) return []

            try {
                pictureData = pictureData.replace(/'/g, '"');
                // 如果已经是数组，直接返回
                if (Array.isArray(pictureData)) {
                    return pictureData.filter(url => url && url.trim())
                }

                // 如果是字符串，尝试解析JSON
                if (typeof pictureData === 'string') {
                    const parsed = JSON.parse(pictureData)
                    if (Array.isArray(parsed)) {
                        return parsed.filter(url => url && url.trim())
                    } else {
                        return pictureData.trim() ? [pictureData.trim()] : []
                    }
                }

                return []
            } catch (error) {
                console.error('解析图片URL失败:', error)
                // 如果解析失败，尝试按逗号分割
                return typeof pictureData === 'string' ?
                    pictureData.split(',').map(item => item.trim()).filter(item => item) : []
            }
        }
    },
    created() {
        this.sceneId = this.$route.params.id
        this.sceneName = this.$route.query.name || '未知场景'
        this.fetchHistory()
    }
}
</script>

<style scoped>
.scene-history {
    padding: 20px;
}

.history-card {
    margin-bottom: 20px;
}

.header-with-breadcrumb {
    padding: 0;
}

.pagination {
    margin-top: 20px;
    text-align: right;
}

.el-breadcrumb {
    line-height: 1;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.scene-info h3 {
    margin: 0;
    font-size: 16px;
    color: #303133;
}

.history-actions {
    display: flex;
    gap: 10px;
}

.content-section {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.content-section:last-child {
    border-bottom: none;
}

.section-title {
    margin: 0 0 12px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
}

.section-content {
    line-height: 1.8;
    color: #606266;
}

.image-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.image-item {
    position: relative;
}

.image-slot {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background-color: #f5f7fa;
    color: #909399;
    font-size: 14px;
}

.image-slot i {
    font-size: 32px;
    margin-bottom: 8px;
}
</style>
